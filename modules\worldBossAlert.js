const fs = require('fs').promises;
const path = require('path');
const { createCanvas } = require('canvas');
const { AttachmentBuilder, EmbedBuilder } = require('discord.js');
const chalk = require('chalk');
const serverConfigs = require('../config/serverConfigs');

class WorldBossAlert {
    constructor(client) {
        this.client = client;
        this.watchFolder = path.join(__dirname, '../worldboss_alerts');
        this.processedFiles = new Set();
        this.isWatching = false;

        // Get shared configuration
        this.sharedConfig = serverConfigs.shared;
        this.bossColors = this.sharedConfig.worldBossColors;
    }

    async ensureWatchFolder() {
        try {
            await fs.mkdir(this.watchFolder, { recursive: true });
        } catch (error) {
            // Folder already exists
        }
    }

async createWorldBossImage(worldBossData) {
    try {
        // Canvas dimensions - same as dungeon alerts
        const canvas = createCanvas(800, 500);
        const ctx = canvas.getContext('2d');

        // Use world boss color theme (similar to S rank)
        const primaryColor = '#aa44ff';

        // Fill background with dark gradient (same as dungeon alerts)
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#0f1016');
        gradient.addColorStop(1, '#1a1b26');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add magical particles effect (same as dungeon alerts)
        this.drawMagicalParticles(ctx, primaryColor, canvas);

        // Draw stylized border (same as dungeon alerts)
        this.drawSoloLevelingBorder(ctx, primaryColor, canvas);

        // Draw header - moved down slightly and reduced space after
        this.drawWorldBossHeader(ctx, canvas.width / 2, 60, primaryColor);

        // Draw status window panel - moved up to reduce gap
        const panelX = 40;
        const panelY = 120; // Reduced from 200 to 120
        const panelWidth = canvas.width - 80;
        const panelHeight = 320; // Increased to accommodate better formatting
        this.drawStatusWindow(ctx, panelX, panelY, panelWidth, panelHeight, primaryColor);

        // Draw world boss info with improved formatting
        let y = 160; // Starting position inside the panel
        const leftMargin = 70;
        const lineSpacing = 130; // Much more spacing between different worlds to fill the box

        // Show world boss information with improved formatting
        const worlds = Object.keys(worldBossData.worldBosses).sort();
        worlds.forEach((worldNum, index) => {
            const worldData = worldBossData.worldBosses[worldNum];
            const currentY = y + (index * lineSpacing);

            // Draw world header
            ctx.font = 'bold 32px "Whitney", Arial, sans-serif';
            ctx.fillStyle = '#88aaff';
            ctx.textAlign = 'left';
            ctx.fillText(`World ${worldNum}`, leftMargin, currentY);

            // Draw Island label in blue, then island name in orange
            ctx.font = 'bold 24px "Whitney", Arial, sans-serif';
            ctx.fillStyle = '#88aaff';
            ctx.fillText(`Island:`, leftMargin + 20, currentY + 35);
            
            const islandLabelWidth = ctx.measureText('Island:').width;
            ctx.fillStyle = '#ffaa44';
            ctx.fillText(` ${worldData.island}`, leftMargin + 20 + islandLabelWidth, currentY + 35);

            // Draw Boss label in blue, then boss name in red
            ctx.fillStyle = '#88aaff';
            ctx.fillText(`Boss:`, leftMargin + 20, currentY + 65);
            
            const bossLabelWidth = ctx.measureText('Boss:').width;
            ctx.fillStyle = '#ff4444';
            ctx.shadowColor = 'rgba(255, 68, 68, 0.5)';
            ctx.shadowBlur = 8;
            ctx.fillText(` ${worldData.boss}`, leftMargin + 20 + bossLabelWidth, currentY + 65);
            ctx.shadowBlur = 0;
        });

        // Add watermark (same as dungeon alerts)
        ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
        ctx.fillStyle = primaryColor.replace(')', ', 0.8)').replace('rgb', 'rgba');
        ctx.textAlign = 'center';
        ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);

        return canvas.toBuffer('image/png');
    } catch (error) {
        console.error(chalk.red('❌ Error creating world boss image:'), error);
        return null;
    }
}

    // Helper methods copied from dungeon alert to maintain visual consistency
    drawMagicalParticles(ctx, color, canvas) {
        const particleCount = 100;

        for (let i = 0; i < particleCount; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 2 + 1;
            const alpha = Math.random() * 0.5 + 0.1;

            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fillStyle = color.replace(')', `, ${alpha})`).replace('rgb', 'rgba');
            ctx.fill();
        }

        for (let i = 0; i < 20; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 4 + 2;

            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);

            const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 2);
            gradient.addColorStop(0, color.replace(')', ', 0.8)').replace('rgb', 'rgba'));
            gradient.addColorStop(1, color.replace(')', ', 0)').replace('rgb', 'rgba'));

            ctx.fillStyle = gradient;
            ctx.fill();
        }
    }

    drawSoloLevelingBorder(ctx, color, canvas) {
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;

        const segments = 30;
        const borderMargin = 15;

        for (let i = 0; i < segments; i++) {
            const topX1 = borderMargin + (i * (canvas.width - borderMargin * 2) / segments);
            const topX2 = borderMargin + ((i + 1) * (canvas.width - borderMargin * 2) / segments);

            ctx.beginPath();
            ctx.moveTo(topX1, borderMargin);
            ctx.lineTo(topX2, borderMargin);
            if (i % 2 === 0) {
                ctx.lineTo((topX1 + topX2) / 2, borderMargin - 5);
                ctx.closePath();
            }
            ctx.stroke();

            const bottomY = canvas.height - borderMargin;
            ctx.beginPath();
            ctx.moveTo(topX1, bottomY);
            ctx.lineTo(topX2, bottomY);
            if (i % 2 === 0) {
                ctx.lineTo((topX1 + topX2) / 2, bottomY + 5);
                ctx.closePath();
            }
            ctx.stroke();

            if (i < segments / 2) {
                const sideY1 = borderMargin + (i * (canvas.height - borderMargin * 2) / (segments / 2));
                const sideY2 = borderMargin + ((i + 1) * (canvas.height - borderMargin * 2) / (segments / 2));

                ctx.beginPath();
                ctx.moveTo(borderMargin, sideY1);
                ctx.lineTo(borderMargin, sideY2);
                if (i % 2 === 0) {
                    ctx.lineTo(borderMargin - 5, (sideY1 + sideY2) / 2);
                    ctx.closePath();
                }
                ctx.stroke();

                const rightX = canvas.width - borderMargin;
                ctx.beginPath();
                ctx.moveTo(rightX, sideY1);
                ctx.lineTo(rightX, sideY2);
                if (i % 2 === 0) {
                    ctx.lineTo(rightX + 5, (sideY1 + sideY2) / 2);
                    ctx.closePath();
                }
                ctx.stroke();
            }
        }

        const cornerSize = 40;

        ctx.beginPath();
        ctx.moveTo(borderMargin, borderMargin + cornerSize);
        ctx.lineTo(borderMargin, borderMargin);
        ctx.lineTo(borderMargin + cornerSize, borderMargin);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(canvas.width - borderMargin - cornerSize, borderMargin);
        ctx.lineTo(canvas.width - borderMargin, borderMargin);
        ctx.lineTo(canvas.width - borderMargin, borderMargin + cornerSize);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(borderMargin, canvas.height - borderMargin - cornerSize);
        ctx.lineTo(borderMargin, canvas.height - borderMargin);
        ctx.lineTo(borderMargin + cornerSize, canvas.height - borderMargin);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(canvas.width - borderMargin - cornerSize, canvas.height - borderMargin);
        ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin);
        ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin - cornerSize);
        ctx.stroke();
    }

    drawWorldBossHeader(ctx, x, y, color) {
        ctx.shadowColor = color;
        ctx.shadowBlur = 20;
        ctx.font = 'bold 42px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText("WORLD BOSS ALERT", x, y);

        ctx.beginPath();
        ctx.moveTo(x - 230, y + 10);
        ctx.lineTo(x - 30, y + 10);
        ctx.moveTo(x + 30, y + 10);
        ctx.lineTo(x + 230, y + 10);
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(x - 240, y + 5);
        ctx.lineTo(x - 240, y + 15);
        ctx.lineTo(x - 230, y + 15);
        ctx.moveTo(x + 240, y + 5);
        ctx.lineTo(x + 240, y + 15);
        ctx.lineTo(x + 230, y + 15);
        ctx.stroke();

        ctx.shadowBlur = 0;
    }



    drawStatusWindow(ctx, x, y, width, height, color) {
        ctx.fillStyle = 'rgba(10, 12, 24, 0.7)';
        ctx.fillRect(x, y, width, height);

        ctx.shadowColor = color;
        ctx.shadowBlur = 10;
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, width, height);
        ctx.shadowBlur = 0;

        ctx.beginPath();
        ctx.moveTo(x + 20, y);
        ctx.lineTo(x + 20, y + 20);
        ctx.lineTo(x, y + 20);
        ctx.strokeStyle = color;
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(x + width - 20, y + height);
        ctx.lineTo(x + width - 20, y + height - 20);
        ctx.lineTo(x + width, y + height - 20);
        ctx.stroke();
    }

    drawSLInfoLine(ctx, _icon, title, description, x, y) {
        ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#88aaff';
        ctx.textAlign = 'left';
        ctx.fillText(title + ":", x, y);

        const titleWidth = ctx.measureText(title + ":").width;
        const descriptionX = x + titleWidth + 20;

        ctx.shadowColor = 'rgba(255, 255, 255, 0.5)';
        ctx.shadowBlur = 5;
        ctx.font = '28px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.fillText(description, descriptionX, y);
        ctx.shadowBlur = 0;
    }

    async sendWorldBossAlert(worldBossData) {
        try {
            // Get all enabled servers for world boss alerts
            const enabledServers = serverConfigs.getEnabledWorldBossServers();

            if (enabledServers.length === 0) {
                console.log(chalk.yellow('⚠️ No servers configured for world boss alerts'));
                return;
            }

            // Create the image once
            const imageBuffer = await this.createWorldBossImage(worldBossData);
            if (!imageBuffer) {
                console.error(chalk.red('❌ Failed to create world boss image'));
                return;
            }

            // Send to all enabled servers
            for (const server of enabledServers) {
                try {
                    const channel = await this.client.channels.fetch(server.config.targetChannelId);
                    if (!channel) {
                        console.error(chalk.red(`❌ Target channel not found for server ${server.name} (${server.serverId})`));
                        continue;
                    }

                    // Create attachment for this server
                    const attachment = new AttachmentBuilder(imageBuffer, { name: 'worldboss_alert.png' });

                    // Create embed
                    const embed = new EmbedBuilder()
                        .setTitle('🌍 WORLD BOSS ALERT')
                        // .setDescription('World bosses have spawned! Check the image for details.')
                         .setImage('attachment://worldboss_alert.png')
                         .setColor('#aa44ff')
                        // .setTimestamp()
                        // .setFooter({ text: 'RankBreaker World Boss Monitor' });

                    // Add fields for each world
                    const worlds = Object.keys(worldBossData.worldBosses).sort();
                    worlds.forEach(worldNum => {
                        const worldData = worldBossData.worldBosses[worldNum];
                        // embed.addFields({
                        //     name: `🌍 World ${worldNum}`,
                        //     value: `**${worldData.island}**\n🔥 ${worldData.boss}`,
                        //     inline: true
                        // });
                    });

                    // Send the message with role ping
                    await channel.send({
                        content: `<@&${server.config.roleId}>`,
                        embeds: [embed],
                        files: [attachment]
                    });

                    console.log(chalk.green(`✅ World boss alert sent to ${server.name}`));

                } catch (serverError) {
                    console.error(chalk.red(`❌ Error sending world boss alert to ${server.name}:`), serverError.message);
                }
            }

        } catch (error) {
            console.error(chalk.red('❌ Error sending world boss alert:'), error);
        }
    }

    async processWorldBossFile(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            const worldBossData = JSON.parse(data);

            console.log(chalk.blue('📊 Processing world boss data:'), worldBossData);

            // Send the alert
            await this.sendWorldBossAlert(worldBossData);

            // Delete the processed file
            await fs.unlink(filePath);
            console.log(chalk.green(`✅ Processed and deleted: ${path.basename(filePath)}`));

        } catch (error) {
            console.error(chalk.red('❌ Error processing world boss file:'), error);
        }
    }

    async watchForWorldBossFiles() {
        try {
            const files = await fs.readdir(this.watchFolder);
            
            for (const file of files) {
                if (file.endsWith('.json') && file.startsWith('worldboss_') && !this.processedFiles.has(file)) {
                    this.processedFiles.add(file);
                    const filePath = path.join(this.watchFolder, file);
                    
                    console.log(chalk.cyan(`🔍 Found new world boss file: ${file}`));
                    await this.processWorldBossFile(filePath);
                }
            }
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error(chalk.red('❌ Error watching for world boss files:'), error);
            }
        }
    }

    async initialize() {
        try {
            console.log(chalk.blue('🌍 Initializing World Boss Alert system...'));
            
            await this.ensureWatchFolder();
            
            // Start watching for files
            this.isWatching = true;
            this.watchInterval = setInterval(() => {
                if (this.isWatching) {
                    this.watchForWorldBossFiles();
                }
            }, 2000); // Check every 2 seconds

            console.log(chalk.green('✅ World Boss Alert system initialized'));
            console.log(chalk.yellow(`📁 Watching folder: ${this.watchFolder}`));

            // Log enabled servers
            const enabledServers = serverConfigs.getEnabledWorldBossServers();
            console.log(chalk.yellow(`🌍 Enabled servers: ${enabledServers.length}`));
            enabledServers.forEach(server => {
                console.log(chalk.yellow(`  - ${server.name}: Channel ${server.config.targetChannelId}, Role ${server.config.roleId}`));
            });

        } catch (error) {
            console.error(chalk.red('❌ Failed to initialize World Boss Alert system:'), error);
        }
    }

    async shutdown() {
        console.log(chalk.yellow('🛑 Shutting down World Boss Alert system...'));
        
        this.isWatching = false;
        
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
            this.watchInterval = null;
        }
        
        console.log(chalk.green('✅ World Boss Alert system shut down successfully'));
    }

    getStatus() {
        const enabledServers = serverConfigs.getEnabledWorldBossServers();
        return {
            isWatching: this.isWatching,
            watchFolder: this.watchFolder,
            enabledServers: enabledServers.length,
            servers: enabledServers.map(s => ({ name: s.name, serverId: s.serverId })),
            processedFiles: this.processedFiles.size
        };
    }
}

module.exports = WorldBossAlert;
