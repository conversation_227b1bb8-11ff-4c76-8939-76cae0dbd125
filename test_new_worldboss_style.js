// Test the new dungeon-style world boss alert
const fs = require('fs');
const path = require('path');

// Create test world boss data with the new style
const testWorldBossData = {
  title: 'WORLD BOSS ALERT',
  worldBosses: {
    '1': {
      island: 'Faceheal Town',
      boss: 'Baizen'
    },
    '2': {
      island: 'Kindama City', 
      boss: 'Alien'
    }
  },
  originalMessage: '** **\n\n**The World Boss Spawned in :**\n\n\n# W1: FACEHEAL\n# W2: KINDAMA\n<@&1375862041251811468>\n-# made by walnuss1 (<PERSON><PERSON>y Mod)',
  timestamp: new Date().toISOString(),
  messageId: 'test_new_style_123',
  authorId: 'test_author_456',
  type: 'world_boss'
};

// Create the worldboss_alerts folder if it doesn't exist
const alertsFolder = path.join(__dirname, 'worldboss_alerts');
if (!fs.existsSync(alertsFolder)) {
  fs.mkdirSync(alertsFolder, { recursive: true });
  console.log('Created worldboss_alerts folder');
}

// Generate unique filename
const filename = `worldboss_${Date.now()}_new_style_test.json`;
const filePath = path.join(alertsFolder, filename);

// Write test data to file
fs.writeFileSync(filePath, JSON.stringify(testWorldBossData, null, 2));

console.log(`✅ Created test world boss alert file with NEW DUNGEON STYLE: ${filename}`);
console.log('📁 File location:', filePath);
console.log('🎨 This will test the new dungeon-style image generation!');
console.log('🔍 The WorldBossAlert module should detect this file and create a dungeon-style image alert!');
console.log('\nTest data:');
console.log(JSON.stringify(testWorldBossData, null, 2));