const { Embed<PERSON>uilder, AttachmentBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const chalk = require('chalk');
const { createCanvas } = require('canvas');
const serverConfigs = require('../config/serverConfigs');

class DungeonAlert {
  constructor(client) {
    this.client = client;
    this.sharedFolder = path.join(__dirname, '../dungeon_alerts');

    // Create shared folder if it doesn't exist
    if (!fs.existsSync(this.sharedFolder)) {
      fs.mkdirSync(this.sharedFolder, { recursive: true });
      //console.log(chalk.green(`Created dungeon alerts folder: ${this.sharedFolder}`));
    }

    // Get shared configuration
    this.sharedConfig = serverConfigs.shared;
    this.worldIslands = this.sharedConfig.worldIslands;
    this.islandBosses = this.sharedConfig.islandBosses;
  }

  initialize() {
    //console.log(`[DungeonAlert] Initializing watcher for folder: ${this.sharedFolder}`);
    
    // Watch the shared folder for new files
    const watcher = chokidar.watch(this.sharedFolder, {
      ignored: /(^|[\/\\])\../,
      persistent: true
    });
    
    watcher.on('add', (filePath) => {
      //console.log(`[DungeonAlert] New file detected: ${filePath}`);
      this.processNewFile(filePath);
    });
    
    watcher.on('error', (error) => {
      console.error('[DungeonAlert] Watcher error:', error);
    });
    
    console.log('[DungeonAlert] Dungeon Alert system initialized');
  }

  async processNewFile(filePath) {
    try {
      //console.log(`[DungeonAlert] Reading file: ${filePath}`);
      const fileData = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(fileData);

      //console.log(`[DungeonAlert] Processing data:`, JSON.stringify({
      //  title: data.title,
      //  type: data.type,
      //  messageId: data.messageId
      //}));

      if (data.type === 'user_message') {
        await this.processUserDungeonMessage(data);
      } else {
        // Default to embed processing for backward compatibility
        await this.processDungeonEmbed(data);
      }

      //console.log(`[DungeonAlert] Deleting processed file: ${filePath}`);
      fs.unlinkSync(filePath);
    } catch (error) {
      console.error(chalk.red('[DungeonAlert] Error processing file:'), error);
    }
  }

  async processUserDungeonMessage(userData) {
    try {
      //console.log('[DungeonAlert] Processing user dungeon message');

      // Create dungeon info from user data using island-specific boss and stored details from last embed
      const dungeonInfo = {
        world: userData.world,
        island: userData.island,
        originalMessage: userData.originalMessage,
        // Use island-specific boss, but other details from the last dungeon embed
        boss: userData.boss || this.islandBosses[userData.island] || 'Unknown',
        rank: userData.rank || 'S',
        isRedDungeon: userData.isRedDungeon !== undefined ? userData.isRedDungeon : true,
        isDoubleDungeon: userData.isDoubleDungeon !== undefined ? userData.isDoubleDungeon : true
      };

      // Send to all enabled servers
      await this.sendDungeonAlertToServers(dungeonInfo, userData.title);

    } catch (error) {
      console.error('[DungeonAlert] Error processing user dungeon message:', error);
      throw error;
    }
  }

  async processDungeonEmbed(embedData) {
    try {
      //console.log('[DungeonAlert] Parsing dungeon info from description');
      const dungeonInfo = this.parseDungeonInfo(embedData.description);

      if (!dungeonInfo) {
        //console.log('[DungeonAlert] Failed to parse dungeon info - skipping');
        return;
      }

      // Add world information to dungeon info
      dungeonInfo.world = this.getWorldFromIsland(dungeonInfo.island);

      //console.log('[DungeonAlert] Parsed dungeon info:', dungeonInfo);

      // Send to all enabled servers
      await this.sendDungeonAlertToServers(dungeonInfo, embedData.title);

    } catch (error) {
      console.error('[Dungeon Alert] Error processing Dungeon embed:', error);
      throw error;
    }
  }

  async sendDungeonAlertToServers(dungeonInfo, title) {
    try {
      // Get all enabled servers for dungeon alerts
      const enabledServers = serverConfigs.getEnabledDungeonServers();

      if (enabledServers.length === 0) {
        console.log(chalk.yellow('⚠️ No servers configured for dungeon alerts'));
        return;
      }

      // Create the image once
      const attachment = await this.createDungeonImage(dungeonInfo);
      const rankColor = this.getRankColor(dungeonInfo.rank);

      // Send to all enabled servers
      for (const server of enabledServers) {
        try {
          const serverConfig = server.config;
          const channel = await this.client.channels.fetch(serverConfig.targetChannelId);

          if (!channel) {
            console.error(chalk.red(`❌ Target channel not found for server ${server.name} (${server.serverId})`));
            continue;
          }

          // Build role pings for this server
          const rolesToPing = [];

          if (serverConfig.dungeonRoles.DUNGEON_PING) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles.DUNGEON_PING}>`);
          }

          // Add world ping role
          if (dungeonInfo.world && serverConfig.worldRoles[dungeonInfo.world]) {
            rolesToPing.push(`<@&${serverConfig.worldRoles[dungeonInfo.world]}>`);
          }

          // Add rank role
          if (dungeonInfo.rank && serverConfig.dungeonRoles[dungeonInfo.rank]) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles[dungeonInfo.rank]}>`);
          }

          // Add island role
          if (dungeonInfo.island && serverConfig.islandRoles[dungeonInfo.island]) {
            rolesToPing.push(`<@&${serverConfig.islandRoles[dungeonInfo.island]}>`);
          }

          // Add special dungeon roles
          if (dungeonInfo.isRedDungeon && serverConfig.dungeonRoles.RED_DUNGEON) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles.RED_DUNGEON}>`);
          }

          if (dungeonInfo.isDoubleDungeon && serverConfig.dungeonRoles.DOUBLE_DUNGEON) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles.DOUBLE_DUNGEON}>`);
          }

          // Create embed for this server
          const newEmbed = new EmbedBuilder()
            .setTitle(title)
            .setColor(rankColor)
            .setImage('attachment://dungeon-info.png');

          // Create new attachment for this server (Discord requires separate instances)
          const serverAttachment = new AttachmentBuilder(attachment.attachment, { name: 'dungeon-info.png' });

          // Send the message
          await channel.send({
            content: rolesToPing.join(' '),
            embeds: [newEmbed],
            files: [serverAttachment]
          });

          console.log(chalk.green(`✅ Dungeon alert sent to ${server.name}`));

        } catch (serverError) {
          console.error(chalk.red(`❌ Error sending dungeon alert to ${server.name}:`), serverError.message);
        }
      }

    } catch (error) {
      console.error(chalk.red('❌ Error sending dungeon alerts:'), error);
      throw error;
    }
  }

  parseDungeonInfo(description) {
    if (!description) return null;
    
    const yamlMatch = description.match(/```(?:yaml)?\s*([\s\S]*?)```/);
    
    if (!yamlMatch) {
      //console.log('Could not find YAML content in description');
      return null;
    }
    
    const yamlContent = yamlMatch[1].trim();
    const infoLines = yamlContent.split('\n').filter(line => line.trim() !== '');
    
    let islandRaw = this.extractValue(infoLines.find(line => line.includes('Island')));
    let island = this.normalizeIslandName(islandRaw);
    
    const dungeonInfo = {
      island: island,
      originalIsland: islandRaw,
      map: this.extractValue(infoLines.find(line => line.includes('Map'))),
      boss: this.extractValue(infoLines.find(line => line.includes('Boss'))),
      rank: this.extractValue(infoLines.find(line => line.includes('Rank'))),
      isRedDungeon: this.checkForTick(infoLines.find(line => line.includes('Red Dungeon'))),
      isDoubleDungeon: this.checkForTick(infoLines.find(line => line.includes('Double Dungeon')))
    };
    
    return dungeonInfo;
  }

  getWorldFromIsland(islandName) {
    if (!islandName) return null;

    for (const [world, islands] of Object.entries(this.worldIslands)) {
      if (islands.includes(islandName)) {
        return parseInt(world);
      }
    }
    return null;
  }

  normalizeIslandName(islandName) {
    if (!islandName) return '';

    const normalized = islandName.trim();

    // Check if worldIslands is available
    if (!this.worldIslands) {
      console.error('[DungeonAlert] worldIslands is undefined! Falling back to normalized name.');
      return normalized;
    }

    // Get all known islands from the shared world islands configuration
    const allIslands = [];
    for (const islands of Object.values(this.worldIslands)) {
      allIslands.push(...islands);
    }

    // Direct match
    for (const island of allIslands) {
      if (island.toLowerCase() === normalized.toLowerCase()) {
        return island;
      }
    }

    // Partial match
    for (const island of allIslands) {
      if (normalized.toLowerCase().includes(island.toLowerCase()) ||
          island.toLowerCase().includes(normalized.toLowerCase())) {
        return island;
      }
    }

    return normalized;
  }

  extractValue(line) {
    if (!line) return '';
    
    const match = line.match(/:\s*(.+)$/);
    if (match) {
      let value = match[1].trim();
      value = value.replace(/[✅❌]\s*(Yes|No)$/, '').trim();
      return value;
    }
    return '';
  }

  checkForTick(line) {
    if (!line) return false;
    return line.includes('✅') || line.includes('Yes');
  }

  async createDungeonImage(dungeonInfo) {
    const canvas = createCanvas(800, 500);
    const ctx = canvas.getContext('2d');
    
    const rankColor = this.getRankColor(dungeonInfo.rank);
    
    // Fill background with dark gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#0f1016');
    gradient.addColorStop(1, '#1a1b26');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add magical particles effect
    this.drawMagicalParticles(ctx, rankColor, canvas);
    
    // Draw stylized border
    this.drawSoloLevelingBorder(ctx, rankColor, canvas);
    
    // Draw header
    this.drawGateHeader(ctx, canvas.width / 2, 60, rankColor);
    
    // Draw rank
    this.drawSoloLevelingRank(ctx, dungeonInfo.rank, canvas.width / 2, 140);
    
    // Draw status window panel
    const panelX = 40;
    const panelY = 200;
    const panelWidth = canvas.width - 80;
    const panelHeight = 240;
    this.drawStatusWindow(ctx, panelX, panelY, panelWidth, panelHeight, rankColor);
    
    // Draw dungeon info
    let y = 240;
    const leftMargin = 70;
    const lineSpacing = 60;

    // Show World instead of Island, and Island instead of Map
    this.drawSLInfoLine(ctx, '', 'World', dungeonInfo.world ? `World ${dungeonInfo.world}` : 'Unknown', leftMargin, y);
    y += lineSpacing;

    this.drawSLInfoLine(ctx, '', 'Island', dungeonInfo.island || 'Unknown', leftMargin, y);
    y += lineSpacing;

    this.drawSLInfoLine(ctx, '', 'Boss', dungeonInfo.boss || 'Unknown', leftMargin, y);
    y += lineSpacing;
    
    // Special indicators
    const specialY = y;
    const halfWidth = canvas.width / 2;
    
    this.drawSLStatusIndicator(ctx, '', 'Red Gate', dungeonInfo.isRedDungeon, leftMargin, specialY);
    this.drawSLStatusIndicator(ctx, '', 'Double Dungeon', dungeonInfo.isDoubleDungeon, halfWidth - 30, specialY);
    
    // Add watermark
    ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
    ctx.fillStyle = rankColor.replace(')', ', 0.8)').replace('rgb', 'rgba');
    ctx.textAlign = 'center';
    ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);
    
    const buffer = canvas.toBuffer();
    return new AttachmentBuilder(buffer, { name: 'dungeon-info.png' });
  }

  drawMagicalParticles(ctx, color, canvas) {
    const particleCount = 100;
    
    for (let i = 0; i < particleCount; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const size = Math.random() * 2 + 1;
      const alpha = Math.random() * 0.5 + 0.1;
      
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fillStyle = color.replace(')', `, ${alpha})`).replace('rgb', 'rgba');
      ctx.fill();
    }
    
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const size = Math.random() * 4 + 2;
      
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 2);
      gradient.addColorStop(0, color.replace(')', ', 0.8)').replace('rgb', 'rgba'));
      gradient.addColorStop(1, color.replace(')', ', 0)').replace('rgb', 'rgba'));
      
      ctx.fillStyle = gradient;
      ctx.fill();
    }
  }

  drawSoloLevelingBorder(ctx, color, canvas) {
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    
    const segments = 30;
    const borderMargin = 15;
    
    for (let i = 0; i < segments; i++) {
      const topX1 = borderMargin + (i * (canvas.width - borderMargin * 2) / segments);
      const topX2 = borderMargin + ((i + 1) * (canvas.width - borderMargin * 2) / segments);
      
      ctx.beginPath();
      ctx.moveTo(topX1, borderMargin);
      ctx.lineTo(topX2, borderMargin);
      if (i % 2 === 0) {
        ctx.lineTo((topX1 + topX2) / 2, borderMargin - 5);
        ctx.closePath();
      }
      ctx.stroke();
      
      const bottomY = canvas.height - borderMargin;
      ctx.beginPath();
      ctx.moveTo(topX1, bottomY);
      ctx.lineTo(topX2, bottomY);
      if (i % 2 === 0) {
        ctx.lineTo((topX1 + topX2) / 2, bottomY + 5);
        ctx.closePath();
      }
      ctx.stroke();
      
      if (i < segments / 2) {
        const sideY1 = borderMargin + (i * (canvas.height - borderMargin * 2) / (segments / 2));
        const sideY2 = borderMargin + ((i + 1) * (canvas.height - borderMargin * 2) / (segments / 2));
        
        ctx.beginPath();
        ctx.moveTo(borderMargin, sideY1);
        ctx.lineTo(borderMargin, sideY2);
        if (i % 2 === 0) {
          ctx.lineTo(borderMargin - 5, (sideY1 + sideY2) / 2);
          ctx.closePath();
        }
        ctx.stroke();
        
        const rightX = canvas.width - borderMargin;
        ctx.beginPath();
        ctx.moveTo(rightX, sideY1);
        ctx.lineTo(rightX, sideY2);
        if (i % 2 === 0) {
          ctx.lineTo(rightX + 5, (sideY1 + sideY2) / 2);
          ctx.closePath();
        }
        ctx.stroke();
      }
    }
    
    const cornerSize = 40;
    
    ctx.beginPath();
    ctx.moveTo(borderMargin, borderMargin + cornerSize);
    ctx.lineTo(borderMargin, borderMargin);
    ctx.lineTo(borderMargin + cornerSize, borderMargin);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(canvas.width - borderMargin - cornerSize, borderMargin);
    ctx.lineTo(canvas.width - borderMargin, borderMargin);
    ctx.lineTo(canvas.width - borderMargin, borderMargin + cornerSize);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(borderMargin, canvas.height - borderMargin - cornerSize);
    ctx.lineTo(borderMargin, canvas.height - borderMargin);
    ctx.lineTo(borderMargin + cornerSize, canvas.height - borderMargin);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(canvas.width - borderMargin - cornerSize, canvas.height - borderMargin);
    ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin);
    ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin - cornerSize);
    ctx.stroke();
  }

  drawGateHeader(ctx, x, y, color) {
    ctx.shadowColor = color;
    ctx.shadowBlur = 20;
    ctx.font = 'bold 42px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'center';
    ctx.fillText("DUNGEON ALERT", x, y);
    
    ctx.beginPath();
    ctx.moveTo(x - 230, y + 10);
    ctx.lineTo(x - 30, y + 10);
    ctx.moveTo(x + 30, y + 10);
    ctx.lineTo(x + 230, y + 10);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x - 240, y + 5);
    ctx.lineTo(x - 240, y + 15);
    ctx.lineTo(x - 230, y + 15);
    ctx.moveTo(x + 240, y + 5);
    ctx.lineTo(x + 240, y + 15);
    ctx.lineTo(x + 230, y + 15);
    ctx.stroke();
    
    ctx.shadowBlur = 0;
  }

  drawSoloLevelingRank(ctx, rank, x, y) {
    const rankColor = this.getRankColor(rank);
    
    this.drawHexagon(ctx, x, y, 55, rankColor);
    
    ctx.shadowColor = rankColor;
    ctx.shadowBlur = 15;
    ctx.font = 'bold 70px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(rank, x, y);
    ctx.shadowBlur = 0;
    ctx.textBaseline = 'alphabetic';
  }

  drawHexagon(ctx, x, y, size, color) {
    ctx.beginPath();
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI / 3);
      const px = x + size * Math.cos(angle);
      const py = y + size * Math.sin(angle);
      if (i === 0) ctx.moveTo(px, py);
      else ctx.lineTo(px, py);
    }
    ctx.closePath();
    
    ctx.shadowColor = color;
    ctx.shadowBlur = 15;
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.stroke();
    
    const gradientFill = ctx.createRadialGradient(x, y, 0, x, y, size);
    gradientFill.addColorStop(0, color.replace(')', ', 0.2)').replace('rgb', 'rgba'));
    gradientFill.addColorStop(1, color.replace(')', ', 0.05)').replace('rgb', 'rgba'));
    ctx.fillStyle = gradientFill;
    ctx.fill();
    ctx.shadowBlur = 0;
  }

  drawStatusWindow(ctx, x, y, width, height, color) {
    ctx.fillStyle = 'rgba(10, 12, 24, 0.7)';
    ctx.fillRect(x, y, width, height);
    
    ctx.shadowColor = color;
    ctx.shadowBlur = 10;
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, width, height);
    ctx.shadowBlur = 0;
    
    ctx.beginPath();
    ctx.moveTo(x + 20, y);
    ctx.lineTo(x + 20, y + 20);
    ctx.lineTo(x, y + 20);
    ctx.strokeStyle = color;
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x + width - 20, y + height);
    ctx.lineTo(x + width - 20, y + height - 20);
    ctx.lineTo(x + width, y + height - 20);
    ctx.stroke();
  }

  drawSLInfoLine(ctx, _icon, title, description, x, y) {
    ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#88aaff';
    ctx.textAlign = 'left';
    ctx.fillText(title + ":", x, y);

    const titleWidth = ctx.measureText(title + ":").width;
    const descriptionX = x + titleWidth + 20;

    ctx.shadowColor = 'rgba(255, 255, 255, 0.5)';
    ctx.shadowBlur = 5;
    ctx.font = '28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.fillText(description, descriptionX, y);
    ctx.shadowBlur = 0;
  }

  drawSLStatusIndicator(ctx, _icon, title, isActive, x, y) {
    ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#88aaff';
    ctx.textAlign = 'left';
    const titleText = title + ":";
    ctx.fillText(titleText, x, y);

    const titleWidth = ctx.measureText(titleText).width;
    const statusX = x + titleWidth + 20;
    ctx.font = '28px "Whitney", Arial, sans-serif';

    if (isActive) {
      ctx.shadowColor = '#4488ff';
      ctx.shadowBlur = 10;
      ctx.fillStyle = '#4488ff';
      ctx.fillText("✔ Yes", statusX, y);
    } else {
      ctx.shadowColor = '#ff4455';
      ctx.shadowBlur = 10;
      ctx.fillStyle = '#ff4455';
      ctx.fillText("✘ No", statusX, y);
    }
    ctx.shadowBlur = 0;
  }

  getRankColor(rank) {
    return this.sharedConfig.rankColors[rank] || '#4488ff';
  }
}

module.exports = DungeonAlert;
